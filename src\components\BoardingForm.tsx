"use client";

import React from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, setHours, setMinutes, isValid } from 'date-fns';
import { useBoarding, Voyage, voyageStatusMap, VoyageStatus, CargoType, cargoTypeMap } from '@/context/BoardingContext';
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext';
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

const cargoTypes: [CargoType, ...CargoType[]] = ['bulk', 'packaged', 'general_cargo', 'equipment'];
const voyageStatuses: [VoyageStatus, ...VoyageStatus[]] = ['planned', 'waiting_arrival', 'outer_anchorage', 'alongside_berth', 'loading', 'discharging', 'finished', 'departed'];

interface BoardingFormProps {
  onFinished: () => void;
  voyageToEdit?: Voyage;
}

export const BoardingForm = ({ onFinished, voyageToEdit }: BoardingFormProps) => {
  const { addVoyage, updateVoyage } = useBoarding();
  const { t } = useTranslation();

  const formSchema = z.object({
    shipName: z.string().min(2, { message: t('ship_name_min_length') }),
    arrivalDate: z.date({
      required_error: t("arrival_date_required"),
    }),
    status: z.enum(voyageStatuses, {
      errorMap: () => ({ message: t("voyage_status_required") }),
    }),
    berthNumber: z.string().optional(),
    loadingStartDate: z.date().optional(),
    loadingEndDate: z.date().optional(),
    dischargingStartDate: z.date().optional(),
    dischargingEndDate: z.date().optional(),
    cargoType: z.enum(cargoTypes).optional(),
    cargoName: z.string().optional(),
    quantity: z.coerce.number().min(0, { message: t('quantity_positive') }).optional(), // New: Quantity field
  }).superRefine((data, ctx) => {
    const statusesRequiringBerth = ['alongside_berth', 'loading', 'discharging', 'departed', 'finished'];
    if (statusesRequiringBerth.includes(data.status) && (!data.berthNumber || data.berthNumber.trim() === '')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: t('berth_number_required_for_status'),
        path: ['berthNumber'],
      });
    }
    if (data.cargoType && (!data.cargoName || data.cargoName.trim() === '')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: t('cargo_name_required'),
        path: ['cargoName'],
      });
    }
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: voyageToEdit ? {
      shipName: voyageToEdit.shipName,
      arrivalDate: new Date(voyageToEdit.arrivalDate),
      status: voyageToEdit.status,
      berthNumber: voyageToEdit.berthNumber || '',
      loadingStartDate: voyageToEdit.loadingStartDate ? new Date(voyageToEdit.loadingStartDate) : undefined,
      loadingEndDate: voyageToEdit.loadingEndDate ? new Date(voyageToEdit.loadingEndDate) : undefined,
      dischargingStartDate: voyageToEdit.dischargingStartDate ? new Date(voyageToEdit.dischargingStartDate) : undefined,
      dischargingEndDate: voyageToEdit.dischargingEndDate ? new Date(voyageToEdit.dischargingEndDate) : undefined,
      cargoType: voyageToEdit.cargoType,
      cargoName: voyageToEdit.cargoName || '',
      quantity: voyageToEdit.quantity, // Set default for quantity
    } : {
      shipName: '',
      arrivalDate: new Date(),
      status: 'planned',
      berthNumber: '',
      loadingStartDate: undefined,
      loadingEndDate: undefined,
      dischargingStartDate: undefined,
      dischargingEndDate: undefined,
      cargoType: undefined,
      cargoName: '',
      quantity: undefined, // Default for new voyage
    },
  });

  const [operationType, setOperationType] = React.useState<'loading' | 'discharging' | undefined>(() => {
    if (voyageToEdit) {
      if (voyageToEdit.loadingStartDate) return 'loading';
      if (voyageToEdit.dischargingStartDate) return 'discharging';
    }
    return undefined;
  });

  const watchedStatus = form.watch('status');
  const watchedBerthNumber = form.watch('berthNumber');
  const watchedCargoType = form.watch('cargoType');
  const isBerthPresent = watchedBerthNumber && watchedBerthNumber.trim() !== '';

  React.useEffect(() => {
    if (['planned', 'waiting_arrival', 'outer_anchorage'].includes(watchedStatus)) {
      form.setValue('berthNumber', '', { shouldValidate: true });
      setOperationType(undefined);
      form.setValue('loadingStartDate', undefined, { shouldValidate: true });
      form.setValue('loadingEndDate', undefined, { shouldValidate: true });
      form.setValue('dischargingStartDate', undefined, { shouldValidate: true });
      form.setValue('dischargingEndDate', undefined, { shouldValidate: true });
    }

    if (watchedStatus === 'loading') {
      setOperationType('loading');
    } else if (watchedStatus === 'discharging') {
      setOperationType('discharging');
    } else if (['departed', 'finished'].includes(watchedStatus)) {
      // For departed/finished, the toggle is not shown, but dates might be present.
    } else if (watchedStatus === 'alongside_berth' && isBerthPresent) {
      // If alongside_berth and berth is present, keep operationType as is or undefined for user selection.
    } else {
      setOperationType(undefined);
    }
  }, [watchedStatus, isBerthPresent, form.setValue]);

  React.useEffect(() => {
    if (operationType === 'loading') {
      form.setValue('dischargingStartDate', undefined, { shouldValidate: true });
      form.setValue('dischargingEndDate', undefined, { shouldValidate: true });
    } else if (operationType === 'discharging') {
      form.setValue('loadingStartDate', undefined, { shouldValidate: true });
      form.setValue('loadingEndDate', undefined, { shouldValidate: true });
    } else if (operationType === undefined && watchedStatus === 'alongside_berth' && isBerthPresent) {
      form.setValue('loadingStartDate', undefined, { shouldValidate: true });
      form.setValue('loadingEndDate', undefined, { shouldValidate: true });
      form.setValue('dischargingStartDate', undefined, { shouldValidate: true });
      form.setValue('dischargingEndDate', undefined, { shouldValidate: true });
    }
  }, [operationType, watchedStatus, isBerthPresent, form.setValue]);

  const availableStatuses = React.useMemo(() => {
    const isBerthPresent = watchedBerthNumber && watchedBerthNumber.trim() !== '';
    if (isBerthPresent) {
      return Object.entries(voyageStatusMap);
    } else {
      return Object.entries(voyageStatusMap).filter(([key]) => 
        !['loading', 'discharging', 'departed', 'finished'].includes(key)
      );
    }
  }, [watchedBerthNumber]);

  const onSubmit: SubmitHandler<z.infer<typeof formSchema>> = (values) => {
    if (voyageToEdit) {
      updateVoyage({ ...voyageToEdit, ...values } as Voyage);
      showSuccess(t('voyage_updated_success'));
    } else {
      addVoyage(values as Omit<Voyage, 'id'>);
      showSuccess(t('voyage_added_success'));
    }
    onFinished();
  };

  // DateField component (Date only)
  const DateField = ({ name, label }: { name: "loadingStartDate" | "loadingEndDate" | "dischargingStartDate" | "dischargingEndDate" | "arrivalDate", label: string }) => {
    const fieldValue = form.watch(name);
    const [popoverOpen, setPopoverOpen] = React.useState(false);

    const handleDateSelect = (date: Date | undefined) => {
      if (date) {
        // Preserve existing time if available, otherwise default to 00:00
        const existingDate = fieldValue && isValid(fieldValue) ? fieldValue : new Date();
        const newDateWithTime = setMinutes(setHours(date, existingDate.getHours()), existingDate.getMinutes());
        form.setValue(name, newDateWithTime, { shouldValidate: true });
      } else {
        form.setValue(name, undefined, { shouldValidate: true });
      }
      setPopoverOpen(false); // Close popover after date selection
    };

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field: formFieldRenderProps }) => (
          <FormItem className="flex flex-col">
            <FormLabel>{label}</FormLabel>
            <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full pl-3 text-left font-normal",
                      !formFieldRenderProps.value && "text-muted-foreground"
                    )}
                  >
                    {formFieldRenderProps.value && isValid(formFieldRenderProps.value) ? (
                      format(formFieldRenderProps.value, "PPP") // Show date only
                    ) : (
                      <span>{t('select_date_placeholder')}</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={formFieldRenderProps.value}
                  onSelect={handleDateSelect}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  // TimeField component (Time only, HH:mm format)
  const TimeField = ({ name, label }: { name: "loadingStartDate" | "loadingEndDate" | "dischargingStartDate" | "dischargingEndDate", label: string }) => {
    const fieldValue = form.watch(name);

    // Local state for the time input string (HH:mm)
    const [localTimeString, setLocalTimeString] = React.useState<string>(() => {
      if (fieldValue && isValid(fieldValue)) {
        return format(fieldValue, 'HH:mm'); // Format to HH:mm
      }
      return ''; // Default to empty string for manual input
    });

    // Update localTimeString when fieldValue changes (e.g., date selected in DateField)
    React.useEffect(() => {
      if (fieldValue && isValid(fieldValue)) {
        setLocalTimeString(format(fieldValue, 'HH:mm'));
      } else {
        setLocalTimeString(''); // Reset if date is cleared
      }
    }, [fieldValue]);

    const handleTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let newTimeString = e.target.value;

      // Allow only digits and one colon, auto-add colon
      const cleaned = newTimeString.replace(/[^0-9:]/g, '');
      let formattedTime = '';
      if (cleaned.length > 0) {
        if (cleaned.length <= 2) {
          formattedTime = cleaned;
        } else if (cleaned.length === 3 && cleaned[2] !== ':') {
          formattedTime = cleaned.substring(0, 2) + ':' + cleaned.substring(2);
        } else if (cleaned.length > 3) {
          formattedTime = cleaned.substring(0, 2) + ':' + cleaned.substring(3, 5);
        }
      }
      formattedTime = formattedTime.substring(0, 5); // Ensure max length HH:MM

      setLocalTimeString(formattedTime);

      // Attempt to parse and update form value only if format is HH:mm
      const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
      if (timeRegex.test(formattedTime)) {
        const [hours, minutes] = formattedTime.split(':').map(Number);
        let dateToUpdate = fieldValue && isValid(fieldValue) ? fieldValue : new Date(); // Use existing date or today's date
        
        const updatedDate = setMinutes(setHours(dateToUpdate, hours), minutes);
        form.setValue(name, updatedDate, { shouldValidate: true });
      } else if (formattedTime === '') {
        // If time input is cleared, set time to 00:00 on the existing date
        if (fieldValue && isValid(fieldValue)) {
          const updatedDate = setMinutes(setHours(fieldValue, 0), 0);
          form.setValue(name, updatedDate, { shouldValidate: true });
        }
      }
    };

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormControl>
              <Input
                type="text" // Changed to text
                pattern="[0-9]{2}:[0-9]{2}" // Pattern for HH:MM
                maxLength={5} // Limit input length to HH:MM
                placeholder="HH:MM" // Placeholder for HH:MM format
                value={localTimeString}
                onChange={handleTimeInputChange}
                className="mt-2"
              />
            </FormControl>
            <FormMessage /> 
          </FormItem>
        )}
      />
    );
  };

  const showLoadingDates = ['loading', 'departed', 'finished'].includes(watchedStatus) || 
                           (watchedStatus === 'alongside_berth' && isBerthPresent && operationType === 'loading');
  const showDischargingDates = ['discharging', 'departed', 'finished'].includes(watchedStatus) || 
                              (watchedStatus === 'alongside_berth' && isBerthPresent && operationType === 'discharging');

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="shipName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('ship_name_label')}</FormLabel>
              <FormControl>
                <Input placeholder={t('ship_name_placeholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <DateField name="arrivalDate" label={t('arrival_date_label')} />
        
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('voyage_status_label')}</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('voyage_status_placeholder')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableStatuses.map(([key, value]) => (
                    <SelectItem key={key} value={key}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        {watchedStatus && !['planned', 'waiting_arrival', 'outer_anchorage'].includes(watchedStatus) && (
          <FormField
            control={form.control}
            name="berthNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('berth_number_label')}</FormLabel>
                <FormControl>
                  <Input placeholder={t('berth_number_placeholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="cargoType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('cargo_type_label')}</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('cargo_type_placeholder')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {cargoTypes.map((key) => (
                    <SelectItem key={key} value={key}>{cargoTypeMap[key]}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {watchedCargoType && (
          <>
            <FormField
              control={form.control}
              name="cargoName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('cargo_name_label')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('cargo_name_placeholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('quantity_label')}</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder={t('quantity_placeholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {watchedStatus === 'alongside_berth' && isBerthPresent && (
          <FormItem>
            <FormLabel>{t('select_operation_type')}</FormLabel>
            <FormControl>
              <ToggleGroup
                type="single"
                value={operationType}
                onValueChange={(value: 'loading' | 'discharging' | undefined) => {
                  setOperationType(value);
                }}
                className="grid grid-cols-2 gap-2"
              >
                <ToggleGroupItem value="loading" aria-label={t('loading_operation')}>
                  {t('loading_operation')}
                </ToggleGroupItem>
                <ToggleGroupItem value="discharging" aria-label={t('discharging_operation')}>
                  {t('discharging_operation')}
                </ToggleGroupItem>
              </ToggleGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}

        {showLoadingDates && (
          <div className="grid grid-cols-2 gap-4">
            <DateField name="loadingStartDate" label={t('loading_start_date_label')} />
            <TimeField name="loadingStartDate" label={t('time_label')} />
            <DateField name="loadingEndDate" label={t('loading_end_date_label')} />
            <TimeField name="loadingEndDate" label={t('time_label')} />
          </div>
        )}

        {showDischargingDates && (
          <div className="grid grid-cols-2 gap-4">
            <DateField name="dischargingStartDate" label={t('discharging_start_date_label')} />
            <TimeField name="dischargingStartDate" label={t('time_label')} />
            <DateField name="dischargingEndDate" label={t('discharging_end_date_label')} />
            <TimeField name="dischargingEndDate" label={t('time_label')} />
          </div>
        )}

        <Button type="submit" className="w-full">
          {voyageToEdit ? t('update_voyage_button') : t('add_voyage_button')}
        </Button>
      </form>
    </Form>
  );
};