import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import LoginPage from "./pages/LoginPage";
import UserManagementPage from "./pages/UserManagementPage";
import EquipmentManagementPage from "./pages/EquipmentManagementPage";
import BoardingManagementPage from "./pages/BoardingManagementPage";
import SafetyManagementPage from "./pages/SafetyManagementPage";
import OperationsManagementPage from "./pages/OperationsManagementPage";
import { AuthProvider } from "./context/AuthContext";
import { EquipmentProvider } from "./context/EquipmentContext";
import { BoardingProvider } from "./context/BoardingContext";
import { SafetyProvider } from "./context/SafetyContext";
import { OperationsProvider } from "./context/OperationsContext";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <EquipmentProvider>
            <BoardingProvider>
              <SafetyProvider>
                <OperationsProvider>
                  <Routes>
                    <Route path="/" element={<Index />} />
                    <Route path="/login" element={<LoginPage />} />
                    <Route path="/user-management" element={<UserManagementPage />} />
                    <Route path="/equipment-management" element={<EquipmentManagementPage />} />
                    <Route path="/boarding-management" element={<BoardingManagementPage />} />
                    <Route path="/safety-management" element={<SafetyManagementPage />} />
                    <Route path="/operations-management" element={<OperationsManagementPage />} />
                    {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </OperationsProvider>
              </SafetyProvider>
            </BoardingProvider>
          </EquipmentProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;