import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./globals.css";
import { LanguageProvider } from "./context/LanguageContext.tsx";
import { TranslationProvider } from "./context/TranslationContext.tsx"; // Import TranslationProvider

createRoot(document.getElementById("root")!).render(
  <LanguageProvider>
    <TranslationProvider> {/* Wrap App with TranslationProvider */}
      <App />
    </TranslationProvider>
  </LanguageProvider>
);