"use client";

import React from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown } from 'lucide-react';
import { useOperations, Operation } from '@/context/OperationsContext';
import { useBoarding } from '@/context/BoardingContext';
import { useEquipment } from '@/context/EquipmentContext';
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext';

interface OperationFormProps {
  onFinished: () => void;
  operationToEdit?: Operation;
}

export const OperationForm = ({ onFinished, operationToEdit }: OperationFormProps) => {
  const { addOperation, updateOperation } = useOperations();
  const { voyages } = useBoarding();
  const { equipment, updateEquipment } = useEquipment();
  const { t } = useTranslation();

  const formSchema = z.object({
    voyageId: z.coerce.number({ required_error: t('voyage_required') }),
    equipmentIds: z.array(z.number()).min(1, { message: t('equipment_required') }),
    notes: z.string().optional(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: operationToEdit ? {
      voyageId: operationToEdit.voyageId,
      equipmentIds: operationToEdit.equipmentIds,
      notes: operationToEdit.notes,
    } : {
      voyageId: undefined,
      equipmentIds: [],
      notes: '',
    },
  });

  const onSubmit: SubmitHandler<z.infer<typeof formSchema>> = (values) => {
    if (operationToEdit) {
      // Handle usage count for modified operation
      const oldEquipmentIds = operationToEdit.equipmentIds;
      const newEquipmentIds = values.equipmentIds;

      // Decrement for removed equipment
      oldEquipmentIds.forEach(oldId => {
        if (!newEquipmentIds.includes(oldId)) {
          const eq = equipment.find(e => e.id === oldId);
          if (eq) updateEquipment({ ...eq, usageCount: Math.max(0, eq.usageCount - 1) });
        }
      });

      // Increment for newly added equipment
      newEquipmentIds.forEach(newId => {
        if (!oldEquipmentIds.includes(newId)) {
          const eq = equipment.find(e => e.id === newId);
          if (eq) updateEquipment({ ...eq, usageCount: eq.usageCount + 1 });
        }
      });

      updateOperation({ ...operationToEdit, ...values, updatedAt: new Date() });
      showSuccess(t('operation_updated_success'));
    } else {
      addOperation(values as Omit<Operation, 'id' | 'createdAt' | 'updatedAt'>);
      // Increment usage count for new operation
      values.equipmentIds.forEach(eqId => {
        const eq = equipment.find(e => e.id === eqId);
        if (eq) updateEquipment({ ...eq, usageCount: eq.usageCount + 1 });
      });
      showSuccess(t('operation_added_success'));
    }
    onFinished();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="voyageId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('ship_name_label')}</FormLabel>
              <Select onValueChange={(val) => field.onChange(Number(val))} value={field.value?.toString()}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('select_voyage_placeholder')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {voyages.map((voyage) => (
                    <SelectItem key={voyage.id} value={voyage.id.toString()}>
                      {voyage.shipName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="equipmentIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('equipment_used')}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="w-full justify-between"
                    >
                      {field.value && field.value.length > 0
                        ? field.value
                            .map((id) => equipment.find((e) => e.id === id)?.name)
                            .filter(Boolean)
                            .join(", ")
                        : t('select_equipment_placeholder')}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                  <Command>
                    <CommandInput placeholder={t('search_equipment_placeholder')} />
                    <CommandEmpty>{t('no_equipment_found')}</CommandEmpty>
                    <CommandGroup>
                      {equipment.map((eq) => (
                        <CommandItem
                          key={eq.id}
                          onSelect={() => {
                            const currentIds = new Set(field.value);
                            if (currentIds.has(eq.id)) {
                              currentIds.delete(eq.id);
                            } else {
                              currentIds.add(eq.id);
                            }
                            field.onChange(Array.from(currentIds));
                          }}
                        >
                          <Checkbox
                            checked={field.value?.includes(eq.id)}
                            onCheckedChange={(checked) => {
                              const currentIds = new Set(field.value);
                              if (checked) {
                                currentIds.add(eq.id);
                              } else {
                                currentIds.delete(eq.id);
                              }
                              field.onChange(Array.from(currentIds));
                            }}
                            className="mr-2"
                          />
                          {eq.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('notes')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('notes')}
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          {operationToEdit ? t('update_operation_button') : t('add_operation_button')}
        </Button>
      </form>
    </Form>
  );
};