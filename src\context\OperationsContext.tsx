"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { loadFromLocalStorage, saveToLocalStorage } from '@/lib/localStorageUtils';

export interface Operation {
  id: number;
  voyageId: number;
  equipmentIds: number[];
  notes: string;
  createdAt: Date; // New
  updatedAt: Date; // New
}

interface OperationsContextType {
  operations: Operation[];
  addOperation: (operation: Omit<Operation, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateOperation: (operation: Operation) => void;
  deleteOperation: (operationId: number) => void;
}

const OperationsContext = createContext<OperationsContextType | undefined>(undefined);

const defaultInitialOperations: Operation[] = [
  { id: 1, voyageId: 1, equipmentIds: [2, 3], notes: 'تم تخصيص المعدات للتحميل.', createdAt: new Date('2024-01-01T10:00:00Z'), updatedAt: new Date('2024-01-01T10:00:00Z') },
];

export const OperationsProvider = ({ children }: { children: ReactNode }) => {
  const [operations, setOperations] = useState<Operation[]>(() => loadFromLocalStorage('app_operations', defaultInitialOperations));

  // Save operations to localStorage whenever it changes
  useEffect(() => {
    saveToLocalStorage('app_operations', operations);
  }, [operations]);

  const addOperation = (operation: Omit<Operation, 'id' | 'createdAt' | 'updatedAt'>) => {
    setOperations(prev => [...prev, { ...operation, id: Date.now(), createdAt: new Date(), updatedAt: new Date() }]);
  };

  const updateOperation = (updatedOperation: Operation) => {
    setOperations(prev => prev.map(op => op.id === updatedOperation.id ? { ...updatedOperation, updatedAt: new Date() } : op));
  };

  const deleteOperation = (operationId: number) => {
    setOperations(prev => prev.filter(op => op.id !== operationId));
  };

  return (
    <OperationsContext.Provider value={{ operations, addOperation, updateOperation, deleteOperation }}>
      {children}
    </OperationsContext.Provider>
  );
};

export const useOperations = () => {
  const context = useContext(OperationsContext);
  if (context === undefined) {
    throw new Error('useOperations must be used within an OperationsProvider');
  }
  return context;
};