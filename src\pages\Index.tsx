"use client";

import { MadeWithDyad } from "@/components/made-with-dyad";
import { useAuth, roleMap } from "@/context/AuthContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, LogOut, Ship, Wrench, ShieldCheck, Activity } from "lucide-react";
import DashboardOverview from "@/components/DashboardOverview";
import { useLanguage, Language } from "@/context/LanguageContext";
import { useTranslation } from "@/context/TranslationContext"; // Import useTranslation
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const Index = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const { language, setLanguage } = useLanguage();
  const { t } = useTranslation(); // Use the translation hook

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
        <div className="w-full max-w-4xl text-center">
          <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
            {t("welcome_app_title")}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
            {t("login_prompt")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/login">
              <Button size="lg">{t("login_button")}</Button>
            </Link>
          </div>
        </div>
        <MadeWithDyad />
      </div>
    );
  }

  const isAppAdmin = user.role === 'app_admin'; // New admin check
  const canManageUsers = isAppAdmin;
  const canManageEquipment = isAppAdmin || user.role === 'equipment_management';
  const canManageBoarding = isAppAdmin || user.role === 'boarding_management';
  const canManageSafety = isAppAdmin || user.role === 'safety_management';
  const canManageOperations = isAppAdmin || user.role === 'operations_management';

  const managementCards = [];

  // Boarding Management Card
  if (canManageBoarding) {
    managementCards.push(
      <Card key="boarding" className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2"><Ship /> {t("manage_ships")}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 dark:text-gray-400 mb-4">{t("manage_ships_desc")}</p>
          <Link to="/boarding-management">
            <Button>{t("manage_ships")}</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  // Equipment Management Card
  if (canManageEquipment) {
    managementCards.push(
      <Card key="equipment" className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2"><Wrench /> {t("manage_equipment")}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 dark:text-gray-400 mb-4">{t("manage_equipment_desc")}</p>
          <Link to="/equipment-management">
            <Button>{t("manage_equipment")}</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  // User Management Card
  if (canManageUsers) {
    managementCards.push(
      <Card key="users" className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2"><Users /> {t("manage_users")}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 dark:text-gray-400 mb-4">{t("manage_users_desc")}</p>
          <Link to="/user-management">
            <Button>{t("manage_users")}</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  // Safety Management Card
  if (canManageSafety) {
    managementCards.push(
      <Card key="safety" className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2"><ShieldCheck /> {t("manage_safety")}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 dark:text-gray-400 mb-4">{t("manage_safety_desc")}</p>
          <Link to="/safety-management">
            <Button>{t("manage_safety")}</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  // Operations Management Card
  if (canManageOperations) {
    managementCards.push(
      <Card key="operations" className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2"><Activity /> {t("manage_operations")}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 dark:text-gray-400 mb-4">{t("manage_operations_desc")}</p>
          <Link to="/operations-management">
            <Button>{t("manage_operations")}</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="w-full max-w-4xl text-center">
        <h1 className="text-4xl font-bold mb-2 text-gray-900 dark:text-gray-100">
          {t("welcome_user", { username: user.username })}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
          {t("logged_in_as")}: <span className="font-semibold text-primary">{roleMap[user.role]}</span>
        </p>
        
        {isAppAdmin && ( // Only app_admin sees the dashboard overview and language selector
          <>
            <div className="flex justify-end mb-4">
              <Select onValueChange={(value: Language) => setLanguage(value)} defaultValue={language}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={t("choose_language")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ar">{t("arabic")}</SelectItem>
                  <SelectItem value="en">{t("english")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DashboardOverview />
          </>
        )}

        <div className={
          managementCards.length === 1
            ? "flex justify-center w-full" // Center the single card
            : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        }>
          {managementCards}
        </div>

        <div className="mt-8">
          <Button onClick={logout} variant="destructive">
            <LogOut className="mr-2 h-4 w-4" />
            {t("logout_button")}
          </Button>
        </div>
      </div>
      <MadeWithDyad />
    </div>
  );
};

export default Index;