"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ship, Wrench, ShieldCheck, Activity } from 'lucide-react';
import { useBoarding, voyageStatusMap } from '@/context/BoardingContext';
import { useEquipment, equipmentStatusMap } from '@/context/EquipmentContext';
import { useSafety } from '@/context/SafetyContext';
import { useOperations } from '@/context/OperationsContext';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

const DashboardOverview = () => {
  const { voyages } = useBoarding();
  const { equipment } = useEquipment();
  const { reports } = useSafety();
  const { operations } = useOperations();
  const { t } = useTranslation(); // Use the translation hook

  // Boarding Metrics
  const totalVoyages = voyages.length;
  const voyagesByStatus = Object.keys(voyageStatusMap).reduce((acc, statusKey) => {
    const status = statusKey as keyof typeof voyageStatusMap;
    acc[status] = voyages.filter(v => v.status === status).length;
    return acc;
  }, {} as Record<keyof typeof voyageStatusMap, number>);

  // Equipment Metrics
  const totalEquipment = equipment.length;
  const equipmentByStatus = Object.keys(equipmentStatusMap).reduce((acc, statusKey) => {
    const status = statusKey as keyof typeof equipmentStatusMap;
    acc[status] = equipment.filter(e => e.status === status).length;
    return acc;
  }, {} as Record<keyof typeof equipmentStatusMap, number>);

  // Safety Metrics
  const totalSafetyReports = reports.length;

  // Operations Metrics
  const totalOperations = operations.length;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
      {/* Boarding Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('dashboard_ships_stats')}</CardTitle>
          <Ship className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent className="text-center"> {/* Added text-center here */}
          <div className="text-2xl font-bold">{totalVoyages}</div>
          <p className="text-xs text-muted-foreground">{t('dashboard_total_voyages')}</p>
          <div className="mt-2 text-sm">
            {Object.entries(voyagesByStatus).map(([statusKey, count]) => (
              <p key={statusKey} className="text-xs text-muted-foreground">
                {voyageStatusMap[statusKey as keyof typeof voyageStatusMap]}: {count}
              </p>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Equipment Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('dashboard_equipment_stats')}</CardTitle>
          <Wrench className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent className="text-center"> {/* Added text-center here */}
          <div className="text-2xl font-bold">{totalEquipment}</div>
          <p className="text-xs text-muted-foreground">{t('dashboard_total_equipment')}</p>
          <div className="mt-2 text-sm">
            {Object.entries(equipmentByStatus).map(([statusKey, count]) => (
              <p key={statusKey} className="text-xs text-muted-foreground">
                {equipmentStatusMap[statusKey as keyof typeof equipmentStatusMap]}: {count}
              </p>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Safety Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('dashboard_safety_reports')}</CardTitle>
          <ShieldCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent className="text-center"> {/* Added text-center here */}
          <div className="text-2xl font-bold">{totalSafetyReports}</div>
          <p className="text-xs text-muted-foreground">{t('dashboard_total_reports')}</p>
        </CardContent>
      </Card>

      {/* Operations Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('dashboard_recorded_operations')}</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent className="text-center"> {/* Added text-center here */}
          <div className="text-2xl font-bold">{totalOperations}</div>
          <p className="text-xs text-muted-foreground">{t('dashboard_total_operations')}</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardOverview;