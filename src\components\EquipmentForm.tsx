"use client";

import React from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useEquipment, Equipment, equipmentStatusMap, EquipmentStatus } from '@/context/EquipmentContext';
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

const equipmentStatuses: [EquipmentStatus, ...EquipmentStatus[]] = ['new', 'used', 'maintained'];

interface EquipmentFormProps {
  onFinished: () => void;
  equipmentToEdit?: Equipment;
}

export const EquipmentForm = ({ onFinished, equipmentToEdit }: EquipmentFormProps) => {
  const { addEquipment, updateEquipment } = useEquipment();
  const { t } = useTranslation(); // Use the translation hook

  const formSchema = z.object({
    name: z.string().min(2, { message: t('equipment_name_min_length') }),
    type: z.string().min(2, { message: t('equipment_type_min_length') }),
    status: z.enum(equipmentStatuses, {
      errorMap: () => ({ message: t("equipment_status_required") }),
    }),
    purchasePrice: z.coerce.number().min(0, { message: t('equipment_price_positive') }),
    lifespanEstimate: z.string().min(2, { message: t('equipment_lifespan_required') }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: equipmentToEdit ? {
      name: equipmentToEdit.name,
      type: equipmentToEdit.type,
      status: equipmentToEdit.status,
      purchasePrice: equipmentToEdit.purchasePrice,
      lifespanEstimate: equipmentToEdit.lifespanEstimate,
    } : {
      name: '',
      type: '',
      status: 'new',
      purchasePrice: 0,
      lifespanEstimate: '',
    },
  });

  const onSubmit: SubmitHandler<z.infer<typeof formSchema>> = (values) => {
    if (equipmentToEdit) {
      updateEquipment({ ...equipmentToEdit, ...values } as Equipment);
      showSuccess(t('equipment_updated_success'));
    } else {
      addEquipment(values as Omit<Equipment, 'id' | 'usageCount'>);
      showSuccess(t('equipment_added_success'));
    }
    onFinished();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('equipment_name_label')}</FormLabel>
              <FormControl>
                <Input placeholder={t('equipment_name_placeholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('equipment_type_label')}</FormLabel>
              <FormControl>
                <Input placeholder={t('equipment_type_placeholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('equipment_status_label')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('equipment_status_placeholder')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(equipmentStatusMap).map(([key, value]) => (
                    <SelectItem key={key} value={key}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="purchasePrice"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('equipment_purchase_price_label')}</FormLabel>
              <FormControl>
                <Input type="number" placeholder={t('equipment_price_placeholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="lifespanEstimate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('equipment_lifespan_estimate_label')}</FormLabel>
              <FormControl>
                <Input placeholder={t('equipment_lifespan_placeholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          {equipmentToEdit ? t('update_equipment_button') : t('add_equipment_button')}
        </Button>
      </form>
    </Form>
  );
};