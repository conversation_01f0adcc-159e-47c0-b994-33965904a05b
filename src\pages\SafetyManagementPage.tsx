"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useSafety, SafetyReport } from '@/context/SafetyContext'; // Import SafetyReport type
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { SafetyReportForm } from '@/components/SafetyReportForm';
import { PlusCircle, Edit, Trash2 } from 'lucide-react'; // Import Edit and Trash2
import { format, isValid } from 'date-fns'; // Import isValid
import EquipmentUsageTracking from '@/components/EquipmentUsageTracking';
import { showSuccess } from '@/utils/toast'; // Import showSuccess
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

const SafetyManagementPage = () => {
  const { user, logout } = useAuth();
  const { reports, deleteReport } = useSafety(); // Use deleteReport
  const navigate = useNavigate();
  const [isFormOpen, setFormOpen] = useState(false);
  const [reportToEdit, setReportToEdit] = useState<SafetyReport | undefined>(undefined); // State for report being edited
  const { t } = useTranslation(); // Use the translation hook

  // Authorization: Only app_admin or safety_management can access
  if (user?.role !== 'app_admin' && user?.role !== 'safety_management') {
    navigate('/');
    return null;
  }

  const handleAddNew = () => {
    setReportToEdit(undefined); // Clear reportToEdit for new report form
    setFormOpen(true);
  };

  const handleEdit = (report: SafetyReport) => {
    setReportToEdit(report); // Set reportToEdit for edit form
    setFormOpen(true);
  };

  const handleDelete = (reportId: number) => {
    deleteReport(reportId);
    showSuccess(t('report_deleted_success')); // New translation key
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('safety_management_title')}</h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
              {t('safety_management_desc')}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button onClick={() => navigate('/')} variant="outline">
              {t('return_to_dashboard')}
            </Button>
            <Button onClick={logout}>{t('logout_button')}</Button>
          </div>
        </header>

        <div className="grid gap-6">
          <Card>
            <CardHeader className="flex flex-row justify-between items-center">
              <CardTitle>{t('safety_reports_title')}</CardTitle>
              <Dialog open={isFormOpen} onOpenChange={setFormOpen}>
                <DialogTrigger asChild>
                  <Button onClick={handleAddNew}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t('add_new_report')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>{reportToEdit ? t('edit_report_dialog_title') : t('add_report_dialog_title')}</DialogTitle> {/* New translation key */}
                    <DialogDescription>
                      {reportToEdit ? t('edit_report_dialog_desc') : t('add_report_dialog_desc')} {/* New translation key */}
                    </DialogDescription>
                  </DialogHeader>
                  <SafetyReportForm onFinished={() => setFormOpen(false)} reportToEdit={reportToEdit} />
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent className="grid gap-4">
              {reports.length > 0 ? (
                reports.map((report) => (
                  <Card key={report.id}>
                    <CardHeader>
                      <CardTitle className="flex justify-between items-center">
                        <span>{report.title}</span>
                        <div className="flex gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleEdit(report)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>{t('confirm_delete_title')}</AlertDialogTitle>
                                <AlertDialogDescription>
                                  {t('confirm_delete_desc', { username: report.title })}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDelete(report.id)}>
                                  {t('confirm_delete')}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </CardTitle>
                      <CardDescription>
                        {t('reported_by')}: {report.reportedBy}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p>{report.description}</p>
                    </CardContent>
                    <CardFooter>
                      <p className="text-sm text-muted-foreground">
                        {report.date && isValid(report.date) ? format(report.date, 'PPP p') : 'N/A'}
                      </p>
                    </CardFooter>
                  </Card>
                ))
              ) : (
                <p className="text-center text-gray-500 dark:text-gray-400 py-8">
                  {t('no_safety_reports')}
                </p>
              )}
            </CardContent>
          </Card>

          {/* New Equipment Usage Tracking Section */}
          <EquipmentUsageTracking />
        </div>
      </div>
    </div>
  );
};

export default SafetyManagementPage;