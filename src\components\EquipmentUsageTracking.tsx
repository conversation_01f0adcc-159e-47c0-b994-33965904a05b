"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useOperations } from '@/context/OperationsContext';
import { useBoarding } from '@/context/BoardingContext';
import { useEquipment } from '@/context/EquipmentContext';
import { Wrench } from 'lucide-react';
import { format, isValid } from 'date-fns'; // Import isValid
import { useTranslation } from '@/context/TranslationContext';

const EquipmentUsageTracking = () => {
  const { operations } = useOperations();
  const { voyages } = useBoarding();
  const { equipment } = useEquipment();
  const { t } = useTranslation();

  const getVoyageName = (voyageId: number) => voyages.find(v => v.id === voyageId)?.shipName || t('unknown_voyage');
  const getEquipmentDetails = (equipmentIds: number[]) => {
    return equipment
      .filter(e => equipmentIds.includes(e.id))
      .map(e => ({ name: e.name, usageCount: e.usageCount }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wrench className="h-5 w-5 text-primary" />
          {t('equipment_tracking_title')}
        </CardTitle>
        <CardDescription>
          {t('equipment_tracking_desc')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {operations.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-center">{t('operation_table_head')}</TableHead>
                <TableHead className="text-center">{t('ship_table_head')}</TableHead>
                <TableHead className="text-center">{t('used_equipment_table_head')}</TableHead>
                <TableHead className="text-center">{t('notes_table_head')}</TableHead>
                <TableHead className="text-center">{t('created_at_table_head')}</TableHead>
                <TableHead className="text-center">{t('updated_at_table_head')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {operations.map((op) => {
                const usedEquipment = getEquipmentDetails(op.equipmentIds);
                return (
                  <TableRow key={op.id}>
                    <TableCell className="font-medium text-center">{t('operation_table_head')} #{op.id}</TableCell>
                    <TableCell className="text-center">{getVoyageName(op.voyageId)}</TableCell>
                    <TableCell className="text-center">
                      {usedEquipment.length > 0 ? (
                        usedEquipment.map((eq, index) => (
                          <div key={index}>
                            {eq.name} ({eq.usageCount})
                          </div>
                        ))
                      ) : (
                        t('no_equipment')
                      )}
                    </TableCell>
                    <TableCell className="text-center">{op.notes || t('no_notes')}</TableCell>
                    <TableCell className="text-center">
                      {op.createdAt && isValid(op.createdAt) ? format(op.createdAt, 'PPP p') : 'N/A'}
                    </TableCell>
                    <TableCell className="text-center">
                      {op.updatedAt && isValid(op.updatedAt) ? format(op.updatedAt, 'PPP p') : 'N/A'}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        ) : (
          <p className="text-center text-gray-500 dark:text-gray-400 py-8">
            {t('no_operations_recorded')}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default EquipmentUsageTracking;