"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { loadFromLocalStorage, saveToLocalStorage } from '@/lib/localStorageUtils';

export interface SafetyReport {
  id: number;
  title: string;
  description: string;
  date: Date;
  reportedBy: string;
}

interface SafetyContextType {
  reports: SafetyReport[];
  addReport: (report: Omit<SafetyReport, 'id'>) => void;
  updateReport: (report: SafetyReport) => void; // New
  deleteReport: (reportId: number) => void; // New
}

const SafetyContext = createContext<SafetyContextType | undefined>(undefined);

const defaultInitialReports: SafetyReport[] = [
  { id: 1, title: 'تسرب زيت بسيط', description: 'تم ملاحظة تسرب زيت بسيط بالقرب من الرافعة رقم 3. تم احتواؤه وتنظيفه.', date: new Date(), reportedBy: 'safety_officer' },
];

export const SafetyProvider = ({ children }: { children: ReactNode }) => {
  const [reports, setReports] = useState<SafetyReport[]>(() => loadFromLocalStorage('app_safety_reports', defaultInitialReports));

  // Save reports to localStorage whenever it changes
  useEffect(() => {
    saveToLocalStorage('app_safety_reports', reports);
  }, [reports]);

  const addReport = (report: Omit<SafetyReport, 'id'>) => {
    setReports(prev => [...prev, { ...report, id: Date.now() }]);
  };

  const updateReport = (updatedReport: SafetyReport) => {
    setReports(prev => prev.map(r => r.id === updatedReport.id ? updatedReport : r));
  };

  const deleteReport = (reportId: number) => {
    setReports(prev => prev.filter(r => r.id !== reportId));
  };

  return (
    <SafetyContext.Provider value={{ reports, addReport, updateReport, deleteReport }}>
      {children}
    </SafetyContext.Provider>
  );
};

export const useSafety = () => {
  const context = useContext(SafetyContext);
  if (context === undefined) {
    throw new Error('useSafety must be used within a SafetyProvider');
  }
  return context;
};