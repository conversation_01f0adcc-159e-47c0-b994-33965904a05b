"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useSafety, SafetyReport } from '@/context/SafetyContext'; // Import SafetyReport type
import { useAuth } from '@/context/AuthContext';
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

interface SafetyReportFormProps {
  onFinished: () => void;
  reportToEdit?: SafetyReport; // Added reportToEdit prop
}

export const SafetyReportForm = ({ onFinished, reportToEdit }: SafetyReportFormProps) => {
  const { addReport, updateReport } = useSafety(); // Use updateReport
  const { user } = useAuth();
  const { t } = useTranslation(); // Use the translation hook

  const formSchema = z.object({
    title: z.string().min(5, { message: t('report_title_min_length') }),
    description: z.string().min(10, { message: t('report_description_min_length') }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: reportToEdit ? {
      title: reportToEdit.title,
      description: reportToEdit.description,
    } : {
      title: '',
      description: '',
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (!user) return;

    if (reportToEdit) {
      updateReport({
        ...reportToEdit,
        title: values.title,
        description: values.description,
        // date and reportedBy remain the same for edits
      });
      showSuccess(t('report_updated_success')); // New translation key
    } else {
      addReport({
        title: values.title,
        description: values.description,
        date: new Date(),
        reportedBy: user.username,
      });
      showSuccess(t('report_added_success'));
    }
    onFinished();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('report_title_label')}</FormLabel>
              <FormControl>
                <Input placeholder={t('report_title_placeholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('report_description_label')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('report_description_placeholder')}
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          {reportToEdit ? t('update_report_button') : t('submit_report_button')} {/* New translation key */}
        </Button>
      </form>
    </Form>
  );
};