"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { loadFromLocalStorage, saveToLocalStorage } from '@/lib/localStorageUtils';

export type VoyageStatus = 'planned' | 'waiting_arrival' | 'outer_anchorage' | 'alongside_berth' | 'loading' | 'discharging' | 'finished' | 'departed'; // Reordered
export const voyageStatusMap: Record<VoyageStatus, string> = {
  planned: 'مخطط لها',
  waiting_arrival: 'انتظار وصول',
  outer_anchorage: 'مخطاف خارجي',
  alongside_berth: 'رباط',
  loading: 'شحن',
  discharging: 'تفريغ',
  finished: 'انتهاء', // Moved up
  departed: 'مغادرة', // Changed text and moved down
};

// Updated: Define CargoType and cargoTypeMap with new order
export type CargoType = 'bulk' | 'packaged' | 'general_cargo' | 'equipment';
export const cargoTypeMap: Record<CargoType, string> = {
  bulk: 'صب',
  packaged: 'معبأ',
  general_cargo: 'بضائع عامة',
  equipment: 'معدات',
};

export interface Voyage {
  id: number;
  shipName: string;
  arrivalDate: Date;
  status: VoyageStatus;
  berthNumber?: string;
  loadingStartDate?: Date;
  loadingEndDate?: Date;
  dischargingStartDate?: Date;
  dischargingEndDate?: Date;
  cargoType?: CargoType;
  cargoName?: string;
  quantity?: number; // New: Quantity of cargo
}

interface BoardingContextType {
  voyages: Voyage[];
  addVoyage: (voyage: Omit<Voyage, 'id'>) => void;
  updateVoyage: (voyage: Voyage) => void;
  deleteVoyage: (voyageId: number) => void;
}

const BoardingContext = createContext<BoardingContextType | undefined>(undefined);

const defaultInitialVoyages: Voyage[] = [
  { id: 1, shipName: 'سفينة الأمل', arrivalDate: new Date('2024-08-15'), status: 'alongside_berth', berthNumber: '5', cargoType: 'general_cargo', cargoName: 'بضائع متنوعة', quantity: 1500 }, // Added cargoName and quantity
  { id: 2, shipName: 'نجمة البحر', arrivalDate: new Date('2024-08-20'), status: 'waiting_arrival', berthNumber: undefined, cargoType: 'bulk', cargoName: 'قمح', quantity: 5000 }, // Added cargoName and quantity
  { id: 3, shipName: 'العملاق', arrivalDate: new Date('2024-08-10'), status: 'departed', berthNumber: undefined, cargoType: 'equipment', cargoName: 'رافعة', quantity: 1 }, // Added cargoName and quantity
  { id: 4, shipName: 'المحيط الهادئ', arrivalDate: new Date('2024-08-22'), status: 'outer_anchorage', berthNumber: undefined, cargoType: 'packaged', cargoName: 'صناديق', quantity: 200 }, // Added cargoName and quantity
];

export const BoardingProvider = ({ children }: { children: ReactNode }) => {
  const [voyages, setVoyages] = useState<Voyage[]>(() => loadFromLocalStorage('app_voyages', defaultInitialVoyages));

  // Save voyages to localStorage whenever it changes
  useEffect(() => {
    saveToLocalStorage('app_voyages', voyages);
  }, [voyages]);

  const addVoyage = (voyage: Omit<Voyage, 'id'>) => {
    setVoyages(prev => [...prev, { ...voyage, id: Date.now() }]);
  };

  const updateVoyage = (updatedVoyage: Voyage) => {
    setVoyages(prev => prev.map(v => v.id === updatedVoyage.id ? updatedVoyage : v));
  };

  const deleteVoyage = (voyageId: number) => {
    setVoyages(prev => prev.filter(v => v.id !== voyageId));
  };

  return (
    <BoardingContext.Provider value={{ voyages, addVoyage, updateVoyage, deleteVoyage }}>
      {children}
    </BoardingContext.Provider>
  );
};

export const useBoarding = () => {
  const context = useContext(BoardingContext);
  if (context === undefined) {
    throw new Error('useBoarding must be used within a BoardingProvider');
  }
  return context;
};