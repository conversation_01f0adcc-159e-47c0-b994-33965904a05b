"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { useLanguage, Language } from './LanguageContext';

// Define translation dictionaries
const translations: Record<Language, Record<string, string>> = {
  ar: {
    "welcome_app_title": "مرحباً بك في تطبيق شركات الشحن والتفريغ البحري",
    "login_prompt": "يرجى تسجيل الدخول للبدء.",
    "login_button": "تسجيل الدخول",
    "welcome_user": "مرحباً بك، {{username}}!",
    "logged_in_as": "أنت مسجل الدخول بصلاحية:",
    "logout_button": "تسجيل الخروج",
    "manage_ships": "لجنة التراكي", // Updated
    "manage_ships_desc": "إدخال بيانات السفن اليومية وتحديث حالتها (مخطاف خارجي، رباط على الرصيف، شحن، تفريغ، مغادرة).",
    "manage_equipment": "إدارة المعدات",
    "manage_equipment_desc": "إضافة وتعديل وتتبع القطع والمعدات.",
    "manage_users": "إدارة المستخدمين",
    "manage_users_desc": "إضافة وتعديل المستخدمين وصلاحياتهم.",
    "manage_safety": "إدارة السلامة",
    "manage_safety_desc": "تسجيل ومتابعة تقارير السلامة.",
    "manage_operations": "إدارة التشغيل",
    "manage_operations_desc": "تنسيق العمليات بين السفن والمعدات.",
    "choose_language": "اختر اللغة",
    "arabic": "العربية",
    "english": "English",
    "login_page_title": "تسجيل الدخول",
    "login_page_desc": "أدخل بيانات الاعتماد الخاصة بك للوصول إلى حسابك.",
    "username_label": "اسم المستخدم",
    "password_label": "كلمة المرور",
    "username_min_length": "اسم المستخدم يجب أن يتكون من حرفين على الأقل.",
    "password_min_length": "كلمة المرور يجب أن تتكون من 6 أحرف على الأقل.",
    "login_success": "تم تسجيل الدخول بنجاح!",
    "login_error": "اسم المستخدم أو كلمة المرور غير صحيحة.",
    "return_to_dashboard": "العودة للوحة التحكم",
    "user_management_title": "إدارة المستخدمين",
    "user_management_desc": "إضافة وتعديل وحذف المستخدمين وصلاحياتهم.",
    "user_list_title": "قائمة المستخدمين",
    "add_new_user": "إضافة مستخدم جديد",
    "add_user_dialog_title": "إضافة مستخدم جديد",
    "add_user_dialog_desc": "أدخل تفاصيل المستخدم الجديد وصلاحيته.",
    "username_table_head": "اسم المستخدم",
    "role_table_head": "الصلاحية",
    "actions_table_head": "إجراءات",
    "confirm_delete_title": "هل أنت متأكد تمامًا؟",
    "confirm_delete_desc": "هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف {{username}} بشكل دائم.",
    "cancel": "إلغاء",
    "confirm_delete": "نعم، قم بالحذف",
    "user_deleted_success": "تم حذف المستخدم بنجاح.",
    "role_selection_placeholder": "اختر صلاحية للمستخدم",
    "add_user_button": "إضافة مستخدم",
    "equipment_management_title": "إدارة القطع والمعدات",
    "equipment_management_desc": "إضافة وتعديل وتتبع جميع قطع ومعدات التشغيل.",
    "equipment_list_title": "قائمة القطع",
    "add_new_equipment": "إضافة قطعة جديدة",
    "edit_equipment": "تعديل قطعة",
    "add_equipment_dialog_title": "إضافة قطعة جديدة",
    "add_equipment_dialog_desc": "أدخل تفاصيل القطعة الجديدة.",
    "edit_equipment_dialog_title": "تعديل قطعة",
    "edit_equipment_dialog_desc": "قم بتحديث تفاصيل القطعة.",
    "equipment_name_label": "اسم القطعة (المعرف)",
    "equipment_type_label": "نوع القطعة",
    "equipment_status_label": "حالة القطعة",
    "equipment_purchase_price_label": "سعر الشراء",
    "equipment_lifespan_estimate_label": "العمر الافتراضي",
    "equipment_name_placeholder": "مثال: Hook-A1",
    "equipment_type_placeholder": "مثال: هوك، ليجز، قفل",
    "equipment_status_placeholder": "اختر حالة القطعة",
    "equipment_price_placeholder": "0.00",
    "equipment_lifespan_placeholder": "مثال: 5 سنوات",
    "update_equipment_button": "تحديث القطعة",
    "add_equipment_button": "إضافة قطعة جديدة",
    "equipment_name_min_length": "اسم القطعة يجب أن يتكون من حرفين على الأقل.",
    "equipment_type_min_length": "نوع القطعة يجب أن يتكون من حرفين على الأقل.",
    "equipment_status_required": "الرجاء اختيار حالة القطعة.",
    "equipment_price_positive": "سعر الشراء يجب أن يكون رقمًا موجبًا.",
    "equipment_lifespan_required": "تقدير العمر الافتراضي مطلوب.",
    "equipment_updated_success": "تم تحديث القطعة بنجاح.",
    "equipment_added_success": "تمت إضافة القطعة بنجاح.",
    "equipment_deleted_success": "تم حذف القطعة بنجاح.",
    "equipment_name_table_head": "الاسم/المعرف",
    "equipment_type_table_head": "النوع",
    "equipment_status_table_head": "الحالة",
    "equipment_usage_count_table_head": "عدد مرات الاستخدام",
    "boarding_management_title": "لجنة التراكي", // Updated
    "boarding_management_desc": "إدخال بيانات السفن اليومية وتحديث حالتها (مخطاف خارجي، رباط على الرصيف، شحن، تفريغ، مغادرة).",
    "voyage_list_title": "قائمة الرحلات",
    "add_new_voyage": "إضافة رحلة جديدة",
    "edit_voyage": "تعديل رحلة",
    "add_voyage_dialog_title": "إضافة رحلة جديدة",
    "add_voyage_dialog_desc": "أدخل تفاصيل الرحلة الجديدة.",
    "edit_voyage_dialog_title": "تعديل رحلة",
    "edit_voyage_dialog_desc": "قم بتحديث تفاصيل الرحلة.",
    "ship_name_label": "اسم السفينة",
    "arrival_date_label": "تاريخ الوصول المتوقع",
    "voyage_status_label": "حالة الرحلة",
    "ship_name_placeholder": "مثال: سفينة الأمل",
    "select_date_placeholder": "اختر تاريخًا",
    "voyage_status_placeholder": "اختر حالة الرحلة",
    "update_voyage_button": "تحديث الرحلة",
    "add_voyage_button": "إضافة رحلة جديدة",
    "ship_name_min_length": "اسم السفينة يجب أن يتكون من حرفين على الأقل.",
    "arrival_date_required": "تاريخ الوصول مطلوب.",
    "voyage_status_required": "الرجاء اختيار حالة الرحلة.",
    "voyage_updated_success": "تم تحديث بيانات الرحلة بنجاح.",
    "voyage_added_success": "تمت إضافة الرحلة بنجاح.",
    "voyage_deleted_success": "تم حذف الرحلة بنجاح.",
    "ship_name_table_head": "اسم السفينة",
    "arrival_date_table_head": "تاريخ الوصول",
    "status_table_head": "الحالة",
    "berth_number_label": "رقم الرصيف",
    "berth_number_placeholder": "مثال: 5",
    "berth_number_table_head": "رقم الرصيف",
    "no_berth_number": "لا يوجد",
    "safety_management_title": "إدارة السلامة",
    "safety_management_desc": "تسجيل ومتابعة تقارير السلامة والحوادث.",
    "safety_reports_title": "تقارير السلامة المسجلة",
    "add_new_report": "إضافة تقرير جديد",
    "add_report_dialog_title": "إضافة تقرير سلامة جديد",
    "add_report_dialog_desc": "قم بتعبئة النموذج لتوثيق حادثة أو ملاحظة تتعلق بالسلامة.",
    "report_title_label": "عنوان التقرير",
    "report_description_label": "وصف الحادثة",
    "report_title_placeholder": "مثال: انزلاق على سطح السفينة",
    "report_description_placeholder": "صف بالتفصيل ما حدث والإجراءات التي تم اتخاذها.",
    "submit_report_button": "إرسال التقرير",
    "report_title_min_length": "عنوان التقرير يجب أن يتكون من 5 أحرف على الأقل.",
    "report_description_min_length": "وصف التقرير يجب أن يتكون من 10 أحرف على الأقل.",
    "report_added_success": "تم إرسال تقرير السلامة بنجاح.",
    "reported_by": "تم الإبلاغ بواسطة:",
    "no_safety_reports": "لا توجد تقارير سلامة مسجلة حاليًا.",
    "equipment_tracking_title": "تتبع وأداء القطع المستخدمة",
    "equipment_tracking_desc": "عرض تفاصيل استخدام المعدات في العمليات المختلفة.",
    "operation_table_head": "العملية",
    "ship_table_head": "السفينة",
    "used_equipment_table_head": "المعدات المستخدمة (عدد الاستخدام)",
    "notes_table_head": "ملاحظات",
    "unknown_voyage": "رحلة غير معروفة",
    "no_equipment": "لا يوجد",
    "no_notes": "لا توجد ملاحظات",
    "no_operations_recorded": "لا توجد عمليات مسجلة لتتبع استخدام المعدات.",
    "operations_management_title": "إدارة التشغيل",
    "operations_management_desc": "متابعة وتنسيق العمليات التشغيلية للسفن والمعدات.",
    "current_operations_title": "العمليات الحالية",
    "operation_for_ship": "عملية للسفينة:",
    "equipment_used": "المعدات المستخدمة:",
    "notes": "ملاحظات:",
    "no_operations_currently": "لا توجد عمليات تشغيلية مسجلة حاليًا.",
    "dashboard_ships_stats": "إحصائيات السفن",
    "dashboard_total_voyages": "إجمالي الرحلات",
    "dashboard_equipment_stats": "إحصائيات المعدات",
    "dashboard_total_equipment": "إجمالي القطع",
    "dashboard_safety_reports": "تقارير السلامة",
    "dashboard_total_reports": "إجمالي التقارير",
    "dashboard_recorded_operations": "العمليات المسجلة",
    "dashboard_total_operations": "إجمالي العمليات",
    "add_new_operation": "إضافة عملية جديدة",
    "edit_operation": "تعديل عملية",
    "add_operation_dialog_title": "إضافة عملية جديدة",
    "add_operation_dialog_desc": "أدخل تفاصيل العملية الجديدة.",
    "edit_operation_dialog_title": "تعديل عملية",
    "edit_operation_dialog_desc": "قم بتحديث تفاصيل العملية.",
    "update_operation_button": "تحديث العملية",
    "add_operation_button": "إضافة عملية",
    "select_voyage_placeholder": "اختر سفينة للعملية",
    "select_equipment_placeholder": "اختر المعدات المستخدمة",
    "search_equipment_placeholder": "ابحث عن معدة...",
    "no_equipment_found": "لم يتم العثور على معدات.",
    "voyage_required": "اسم السفينة مطلوب.",
    "equipment_required": "يجب اختيار معدة واحدة على الأقل.",
    "created_at_table_head": "تاريخ الإنشاء",
    "updated_at_table_head": "تاريخ التعديل",
    "operation_added_success": "تمت إضافة العملية بنجاح.",
    "operation_updated_success": "تم تحديث العملية بنجاح.",
    "operation_deleted_success": "تم حذف العملية بنجاح.",
    "clear_all_data_button": "مسح جميع البيانات",
    "clear_all_data_title": "هل أنت متأكد تمامًا؟",
    "clear_all_data_desc": "لا يمكن التراجع عن هذا الإجراء. سيؤدي هذا إلى حذف جميع بيانات التطبيق (المستخدمين، المعدات، الرحلات، تقارير السلامة، العمليات) بشكل دائم من التخزين المحلي الخاص بك. سيتم تسجيل خروجك.",
    "yes_clear_all": "نعم، مسح الكل",
    "clear_all_data_success": "تم مسح جميع بيانات التطبيق بنجاح!",
    "edit_user_dialog_title": "تعديل المستخدم",
    "edit_user_dialog_desc": "قم بتحديث تفاصيل المستخدم.",
    "update_user_button": "تحديث المستخدم",
    "user_updated_success": "تم تحديث المستخدم بنجاح.",
    "optional": "اختياري",
    "leave_blank_for_no_change": "اتركه فارغًا لعدم التغيير",
    "edit_report_dialog_title": "تعديل تقرير السلامة",
    "edit_report_dialog_desc": "قم بتحديث تفاصيل تقرير السلامة.",
    "update_report_button": "تحديث التقرير",
    "report_updated_success": "تم تحديث تقرير السلامة بنجاح.",
    "report_deleted_success": "تم حذف تقرير السلامة بنجاح.",
    "filter_by_status": "تصفية حسب الحالة",
    "all_voyages": "جميع الرحلات",
    "no_voyages_found_for_filter": "لا توجد رحلات مطابقة للفلتر المحدد.",
    "loading_start_date_label": "تاريخ بدء الشحن",
    "loading_end_date_label": "تاريخ انتهاء الشحن",
    "discharging_start_date_label": "تاريخ بدء التفريغ",
    "discharging_end_date_label": "تاريخ انتهاء التفريغ",
    "no_date_recorded": "لم يتم التسجيل",
    "select_operation_type": "اختر نوع العملية",
    "loading_operation": "شحن",
    "discharging_operation": "تفريغ",
    "berth_number_required_for_status": "رقم الرصيف مطلوب لهذه الحالة.",
    "departed": "مغادرة",
    "app_admin": "مسؤول التطبيق",
    "cargo_type_table_head": "نوع البضاعة",
    "cargo_type_label": "نوع البضاعة",
    "cargo_type_placeholder": "اختر نوع البضاعة",
    "equipment": "معدات",
    "bulk": "صب",
    "packaged": "معبأ",
    "general_cargo": "بضائع عامة",
    "no_cargo_type": "لا يوجد نوع بضاعة",
    "cargo_name_table_head": "اسم البضاعة", // New translation
    "cargo_name_label": "اسم البضاعة", // New translation
    "cargo_name_placeholder": "مثال: قمح، أجهزة إلكترونية", // New translation
    "no_cargo_name": "لا يوجد اسم بضاعة", // New translation
    "cargo_name_required": "اسم البضاعة مطلوب عند تحديد نوع البضاعة.", // New translation
    "time_label": "الوقت", // New translation
    "quantity_label": "الكمية", // New translation
    "quantity_table_head": "الكمية", // New translation
    "quantity_placeholder": "مثال: 1500", // New translation
    "quantity_positive": "الكمية يجب أن تكون رقمًا موجبًا.", // New translation
    "no_quantity": "لا يوجد", // New translation
  },
  en: {
    "welcome_app_title": "Welcome to the Shipping and Discharge Companies Application",
    "login_prompt": "Please log in to get started.",
    "login_button": "Login",
    "welcome_user": "Welcome, {{username}}!",
    "logged_in_as": "You are logged in as:",
    "logout_button": "Logout",
    "manage_ships": "Berthing Committee", // Updated
    "manage_ships_desc": "Daily entry of ship data and continuous updates on vessel status (outer anchorage, alongside berth, loading, discharging, departure).",
    "manage_equipment": "Equipment Management",
    "manage_equipment_desc": "Add, modify, and track parts and equipment.",
    "manage_users": "User Management",
    "manage_users_desc": "Add, modify, and delete users and their permissions.",
    "manage_safety": "Safety Management",
    "manage_safety_desc": "Record and follow up on safety reports.",
    "manage_operations": "Operations Management",
    "manage_operations_desc": "Coordinate operations between ships and equipment.",
    "choose_language": "Choose Language",
    "arabic": "Arabic",
    "english": "English",
    "login_page_title": "Login",
    "login_page_desc": "Enter your credentials to access your account.",
    "username_label": "Username",
    "password_label": "Password",
    "username_min_length": "Username must be at least 2 characters.",
    "password_min_length": "Password must be at least 6 characters.",
    "login_success": "Logged in successfully!",
    "login_error": "Incorrect username or password.",
    "return_to_dashboard": "Return to Dashboard",
    "user_management_title": "User Management",
    "user_management_desc": "Add, modify, and delete users and their permissions.",
    "user_list_title": "User List",
    "add_new_user": "Add New User",
    "add_user_dialog_title": "Add New User",
    "add_user_dialog_desc": "Enter the details and role for the new user.",
    "username_table_head": "Username",
    "role_table_head": "Role",
    "actions_table_head": "Actions",
    "confirm_delete_title": "Are you absolutely sure?",
    "confirm_delete_desc": "This action cannot be undone. This will permanently delete {{username}}.",
    "cancel": "Cancel",
    "confirm_delete": "Yes, Delete",
    "user_deleted_success": "User deleted successfully.",
    "role_selection_placeholder": "Select a role for the user",
    "add_user_button": "Add User",
    "equipment_management_title": "Equipment & Parts Management",
    "equipment_management_desc": "Add, modify, and track all operational parts and equipment.",
    "equipment_list_title": "Parts List",
    "add_new_equipment": "Add New Part",
    "edit_equipment": "Edit Part",
    "add_equipment_dialog_title": "Add New Part",
    "add_equipment_dialog_desc": "Enter the details for the new part.",
    "edit_equipment_dialog_title": "Edit Part",
    "edit_equipment_dialog_desc": "Update the part details.",
    "equipment_name_label": "Part Name (Identifier)",
    "equipment_type_label": "Part Type",
    "equipment_status_label": "Part Status",
    "equipment_purchase_price_label": "Purchase Price",
    "equipment_lifespan_estimate_label": "Estimated Lifespan",
    "equipment_name_placeholder": "Ex: Hook-A1",
    "equipment_type_placeholder": "Ex: Hook, Legs, Lock",
    "equipment_status_placeholder": "Select part status",
    "equipment_price_placeholder": "0.00",
    "equipment_lifespan_placeholder": "Ex: 5 years",
    "update_equipment_button": "Update Part",
    "add_equipment_button": "Add New Part",
    "equipment_name_min_length": "Part name must be at least 2 characters.",
    "equipment_type_min_length": "Part type must be at least 2 characters.",
    "equipment_status_required": "Please select a part status.",
    "equipment_price_positive": "Purchase price must be a positive number.",
    "equipment_lifespan_required": "Estimated lifespan is required.",
    "equipment_updated_success": "Part updated successfully.",
    "equipment_added_success": "Part added successfully.",
    "equipment_deleted_success": "Part deleted successfully.",
    "equipment_name_table_head": "Name/Identifier",
    "equipment_type_table_head": "Type",
    "equipment_status_table_head": "Status",
    "equipment_usage_count_table_head": "Usage Count",
    "boarding_management_title": "Berthing Committee", // Updated
    "boarding_management_desc": "Daily entry of ship data and continuous updates on vessel status (outer anchorage, alongside berth, loading, discharging, departure).",
    "voyage_list_title": "Voyage List",
    "add_new_voyage": "Add New Voyage",
    "edit_voyage": "Edit Voyage",
    "add_voyage_dialog_title": "Add New Voyage",
    "add_voyage_dialog_desc": "Enter the details for the new voyage.",
    "edit_voyage_dialog_title": "Edit Voyage",
    "edit_voyage_dialog_desc": "Update the voyage details.",
    "ship_name_label": "Ship Name",
    "arrival_date_label": "Expected Arrival Date",
    "voyage_status_label": "Voyage Status",
    "ship_name_placeholder": "Ex: Ship of Hope",
    "select_date_placeholder": "Select a date",
    "voyage_status_placeholder": "Select voyage status",
    "update_voyage_button": "Update Voyage",
    "add_voyage_button": "Add New Voyage",
    "ship_name_min_length": "Ship name must be at least 2 characters.",
    "arrival_date_required": "Arrival date is required.",
    "voyage_status_required": "Please select a voyage status.",
    "voyage_updated_success": "Voyage data updated successfully.",
    "voyage_added_success": "Voyage added successfully.",
    "voyage_deleted_success": "Voyage deleted successfully.",
    "ship_name_table_head": "Ship Name",
    "arrival_date_table_head": "Arrival Date",
    "status_table_head": "Status",
    "berth_number_label": "Berth Number",
    "berth_number_placeholder": "Ex: 5",
    "berth_number_table_head": "Berth Number",
    "no_berth_number": "N/A",
    "safety_management_title": "Safety Management",
    "safety_management_desc": "Record and follow up on safety reports and incidents.",
    "safety_reports_title": "Recorded Safety Reports",
    "add_new_report": "Add New Report",
    "add_report_dialog_title": "Add New Safety Report",
    "add_report_dialog_desc": "Fill out the form to document a safety incident or observation.",
    "report_title_label": "Report Title",
    "report_description_label": "Incident Description",
    "report_title_placeholder": "Ex: Slip on deck",
    "report_description_placeholder": "Describe in detail what happened and actions taken.",
    "submit_report_button": "Submit Report",
    "report_title_min_length": "Report title must be at least 5 characters.",
    "report_description_min_length": "Report description must be at least 10 characters.",
    "report_added_success": "Safety report submitted successfully.",
    "reported_by": "Reported by:",
    "no_safety_reports": "No safety reports currently recorded.",
    "equipment_tracking_title": "Equipment Usage & Performance Tracking",
    "equipment_tracking_desc": "View details of equipment usage in various operations.",
    "operation_table_head": "Operation",
    "ship_table_head": "Ship",
    "used_equipment_table_head": "Used Equipment (Usage Count)",
    "notes_table_head": "Notes",
    "unknown_voyage": "Unknown Voyage",
    "no_equipment": "None",
    "no_notes": "No notes",
    "no_operations_recorded": "No operations currently recorded for equipment tracking.",
    "operations_management_title": "Operations Management",
    "operations_management_desc": "Monitor and coordinate operational activities for ships and equipment.",
    "current_operations_title": "Current Operations",
    "operation_for_ship": "Operation for Ship:",
    "equipment_used": "Equipment Used:",
    "notes": "Notes:",
    "no_operations_currently": "No operational activities currently recorded.",
    "dashboard_ships_stats": "Ship Statistics",
    "dashboard_total_voyages": "Total Voyages",
    "dashboard_equipment_stats": "Equipment Statistics",
    "dashboard_total_equipment": "Total Parts",
    "dashboard_safety_reports": "Safety Reports",
    "dashboard_total_reports": "Total Reports",
    "dashboard_recorded_operations": "Recorded Operations",
    "dashboard_total_operations": "Total Operations",
    "add_new_operation": "Add New Operation",
    "edit_operation": "Edit Operation",
    "add_operation_dialog_title": "Add New Operation",
    "add_operation_dialog_desc": "Enter the details for the new operation.",
    "edit_operation_dialog_title": "Edit Operation",
    "edit_operation_dialog_desc": "Update the operation details.",
    "update_operation_button": "Update Operation",
    "add_operation_button": "Add Operation",
    "select_voyage_placeholder": "Select a ship for the operation",
    "select_equipment_placeholder": "Select used equipment",
    "search_equipment_placeholder": "Search equipment...",
    "no_equipment_found": "No equipment found.",
    "voyage_required": "Ship name is required.",
    "equipment_required": "At least one equipment must be selected.",
    "created_at_table_head": "Created At",
    "updated_at_table_head": "Updated At",
    "operation_added_success": "Operation added successfully.",
    "operation_updated_success": "Operation updated successfully.",
    "operation_deleted_success": "Operation deleted successfully.",
    "clear_all_data_button": "Clear All Data",
    "clear_all_data_title": "Are you absolutely sure?",
    "clear_all_data_desc": "This action cannot be undone. This will permanently delete ALL application data (users, equipment, voyages, safety reports, operations) from your local storage. You will be logged out.",
    "yes_clear_all": "Yes, Clear All",
    "clear_all_data_success": "All application data cleared successfully!",
    "edit_user_dialog_title": "Edit User",
    "edit_user_dialog_desc": "Update the user details.",
    "update_user_button": "Update User",
    "user_updated_success": "User updated successfully.",
    "optional": "Optional",
    "leave_blank_for_no_change": "Leave blank for no change",
    "edit_report_dialog_title": "Edit Safety Report",
    "edit_report_dialog_desc": "Update the safety report details.",
    "update_report_button": "Update Report",
    "report_updated_success": "Safety report updated successfully.",
    "report_deleted_success": "Safety report deleted successfully.",
    "filter_by_status": "Filter by Status",
    "all_voyages": "All Voyages",
    "no_voyages_found_for_filter": "No voyages found matching the selected filter.",
    "loading_start_date_label": "Loading Start Date",
    "loading_end_date_label": "Loading End Date",
    "discharging_start_date_label": "Discharging Start Date",
    "discharging_end_date_label": "Discharging End Date",
    "no_date_recorded": "Not Recorded",
    "select_operation_type": "Select Operation Type",
    "loading_operation": "Loading",
    "discharging_operation": "Discharging",
    "berth_number_required_for_status": "Berth number is required for this status.",
    "departed": "Departure",
    "app_admin": "Application Admin",
    "cargo_type_table_head": "Cargo Type",
    "cargo_type_label": "Cargo Type",
    "cargo_type_placeholder": "Select cargo type",
    "equipment": "Equipment",
    "bulk": "Bulk",
    "packaged": "Packaged",
    "general_cargo": "General Cargo",
    "no_cargo_type": "No Cargo Type",
    "cargo_name_table_head": "Cargo Name", // New translation
    "cargo_name_label": "Cargo Name", // New translation
    "cargo_name_placeholder": "Ex: Wheat, Electronic devices", // New translation
    "no_cargo_name": "No Cargo Name", // New translation
    "cargo_name_required": "Cargo name is required when cargo type is selected.", // New translation
    "time_label": "Time", // New translation
    "quantity_label": "Quantity", // New translation
    "quantity_table_head": "Quantity", // New translation
    "quantity_placeholder": "Ex: 1500", // New translation
    "quantity_positive": "Quantity must be a positive number.", // New translation
    "no_quantity": "N/A", // New translation
  },
};

interface TranslationContextType {
  t: (key: string, params?: Record<string, string | number>) => string;
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

export const TranslationProvider = ({ children }: { children: ReactNode }) => {
  const { language } = useLanguage();

  const t = (key: string, params?: Record<string, string | number>): string => {
    let translatedText = translations[language][key] || key; // Fallback to key if not found

    if (params) {
      for (const paramKey in params) {
        translatedText = translatedText.replace(
          new RegExp(`{{${paramKey}}}`, 'g'),
          String(params[paramKey])
        );
      }
    }
    return translatedText;
  };

  return (
    <TranslationContext.Provider value={{ t }}>
      {children}
    </TranslationContext.Provider>
  );
};

export const useTranslation = () => {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
};