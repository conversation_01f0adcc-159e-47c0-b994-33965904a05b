"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useBoarding, Voyage, voyageStatusMap, VoyageStatus, cargoTypeMap } from '@/context/BoardingContext';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { BoardingForm } from '@/components/BoardingForm';
import { Trash2, Edit, PlusCircle } from 'lucide-react';
import { showSuccess } from '@/utils/toast';
import { format, isValid } from 'date-fns';
import { useTranslation } from '@/context/TranslationContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const BoardingManagementPage = () => {
  const { user, logout } = useAuth();
  const { voyages, deleteVoyage } = useBoarding();
  const navigate = useNavigate();
  const [isFormOpen, setFormOpen] = useState(false);
  const [voyageToEdit, setVoyageToEdit] = useState<Voyage | undefined>(undefined);
  const [filterStatus, setFilterStatus] = useState<VoyageStatus | 'all'>('all');
  const { t } = useTranslation();

  // Authorization: Only app_admin or boarding_management can access
  if (user?.role !== 'app_admin' && user?.role !== 'boarding_management') {
    navigate('/');
    return null;
  }

  const handleAddNew = () => {
    setVoyageToEdit(undefined);
    setFormOpen(true);
  };

  const handleEdit = (item: Voyage) => {
    setVoyageToEdit(item);
    setFormOpen(true);
  };

  const handleDelete = (itemId: number) => {
    deleteVoyage(itemId);
    showSuccess(t('voyage_deleted_success'));
  };

  // Filtered voyages based on selected status
  const filteredVoyages = voyages.filter(item => {
    if (filterStatus === 'all') {
      return true;
    }
    return item.status === filterStatus;
  });

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('boarding_management_title')}</h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
              {t('boarding_management_desc')}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button onClick={() => navigate('/')} variant="outline">
              {t('return_to_dashboard')}
            </Button>
            <Button onClick={logout}>{t('logout_button')}</Button>
          </div>
        </header>

        <Card>
          <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle>{t('voyage_list_title')}</CardTitle>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <Select onValueChange={(value: VoyageStatus | 'all') => setFilterStatus(value)} defaultValue={filterStatus}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder={t('filter_by_status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('all_voyages')}</SelectItem>
                  <SelectItem value="loading">{voyageStatusMap['loading']}</SelectItem>
                  <SelectItem value="discharging">{voyageStatusMap['discharging']}</SelectItem>
                </SelectContent>
              </Select>
              <Dialog open={isFormOpen} onOpenChange={setFormOpen}>
                <DialogTrigger asChild>
                  <Button onClick={handleAddNew} className="w-full sm:w-auto">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t('add_new_voyage')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[525px] max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>{voyageToEdit ? t('edit_voyage_dialog_title') : t('add_voyage_dialog_title')}</DialogTitle>
                    <DialogDescription>
                      {voyageToEdit ? t('edit_voyage_dialog_desc') : t('add_voyage_dialog_desc')}
                    </DialogDescription>
                  </DialogHeader>
                  <BoardingForm 
                    onFinished={() => setFormOpen(false)} 
                    voyageToEdit={voyageToEdit}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-center">{t('ship_name_table_head')}</TableHead>
                    <TableHead className="text-center">{t('arrival_date_table_head')}</TableHead>
                    <TableHead className="text-center">{t('status_table_head')}</TableHead>
                    <TableHead className="text-center">{t('berth_number_table_head')}</TableHead>
                    <TableHead className="text-center">{t('cargo_type_table_head')}</TableHead>
                    <TableHead className="text-center">{t('cargo_name_table_head')}</TableHead>
                    <TableHead className="text-center">{t('quantity_table_head')}</TableHead>
                    <TableHead className="text-center">{t('loading_start_date_label')}</TableHead>
                    <TableHead className="text-center">{t('loading_end_date_label')}</TableHead>
                    <TableHead className="text-center">{t('discharging_start_date_label')}</TableHead>
                    <TableHead className="text-center">{t('discharging_end_date_label')}</TableHead>
                    <TableHead className="text-right">{t('actions_table_head')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVoyages.length > 0 ? (
                    filteredVoyages.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium text-center">{item.shipName}</TableCell>
                        <TableCell className="text-center">
                          {item.arrivalDate && isValid(item.arrivalDate) ? format(item.arrivalDate, 'PPP') : '---'}
                        </TableCell>
                        <TableCell className="text-center">{voyageStatusMap[item.status]}</TableCell>
                        <TableCell className="text-center">{item.berthNumber || '---'}</TableCell>
                        <TableCell className="text-center">{item.cargoType ? cargoTypeMap[item.cargoType] : '---'}</TableCell>
                        <TableCell className="text-center">{item.cargoName || '---'}</TableCell>
                        <TableCell className="text-center">{item.quantity !== undefined ? item.quantity : '---'}</TableCell>
                        <TableCell className="text-center">
                          {item.loadingStartDate && isValid(item.loadingStartDate) ? format(item.loadingStartDate, 'PPP HH:mm') : '---'}
                        </TableCell>
                        <TableCell className="text-center">
                          {item.loadingEndDate && isValid(item.loadingEndDate) ? format(item.loadingEndDate, 'PPP HH:mm') : '---'}
                        </TableCell>
                        <TableCell className="text-center">
                          {item.dischargingStartDate && isValid(item.dischargingStartDate) ? format(item.dischargingStartDate, 'PPP HH:mm') : '---'}
                        </TableCell>
                        <TableCell className="text-center">
                          {item.dischargingEndDate && isValid(item.dischargingEndDate) ? format(item.dischargingEndDate, 'PPP HH:mm') : '---'}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="icon" onClick={() => handleEdit(item)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>{t('confirm_delete_title')}</AlertDialogTitle>
                                <AlertDialogDescription>
                                  {t('confirm_delete_desc', { username: item.shipName })}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDelete(item.id)}>
                                  {t('confirm_delete')}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={12} className="text-center text-gray-500 dark:text-gray-400 py-8">
                        {t('no_voyages_found_for_filter')}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BoardingManagementPage;