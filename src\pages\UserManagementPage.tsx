"use client";

import React, { useState } from 'react';
import { useAuth, roleMap } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { UserForm } from '@/components/UserForm';
import { Trash2, HardDrive, Edit } from 'lucide-react'; // Import Edit icon
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

const UserManagementPage = () => {
  const { user, users, logout, deleteUser, clearAllAppData } = useAuth();
  const navigate = useNavigate();
  const [isFormOpen, setFormOpen] = useState(false); // For Add/Edit User Dialog
  const [userToEdit, setUserToEdit] = useState<typeof users[0] | undefined>(undefined); // State for user being edited
  const { t } = useTranslation(); // Use the translation hook

  // Authorization: Only app_admin can access this page
  if (user?.role !== 'app_admin') {
    // Redirect to home if not authorized
    navigate('/');
    return null;
  }

  const handleAddNew = () => {
    setUserToEdit(undefined); // Clear userToEdit for new user form
    setFormOpen(true);
  };

  const handleEdit = (user: typeof users[0]) => {
    setUserToEdit(user); // Set userToEdit for edit form
    setFormOpen(true);
  };

  const handleDelete = (userId: number) => {
    deleteUser(userId);
    showSuccess(t('user_deleted_success'));
  };

  const handleClearAllData = () => {
    clearAllAppData();
    showSuccess(t("clear_all_data_success")); // Use translated message
    // The page will redirect to login automatically due to context reset
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('user_management_title')}</h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
              {t('user_management_desc')}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button onClick={() => navigate('/')} variant="outline">
              {t('return_to_dashboard')}
            </Button>
            <Button onClick={logout}>{t('logout_button')}</Button>
          </div>
        </header>

        <Card>
          <CardHeader className="flex flex-row justify-between items-center">
            <CardTitle>{t('user_list_title')}</CardTitle>
            <div className="flex gap-2">
              <Dialog open={isFormOpen} onOpenChange={setFormOpen}>
                <DialogTrigger asChild>
                  <Button onClick={handleAddNew}>{t('add_new_user')}</Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>{userToEdit ? t('edit_user_dialog_title') : t('add_user_dialog_title')}</DialogTitle> {/* New translation key */}
                    <DialogDescription>
                      {userToEdit ? t('edit_user_dialog_desc') : t('add_user_dialog_desc')} {/* New translation key */}
                    </DialogDescription>
                  </DialogHeader>
                  <UserForm onFinished={() => setFormOpen(false)} userToEdit={userToEdit} />
                </DialogContent>
              </Dialog>
              {user?.role === 'app_admin' && ( // Only app_admin can clear all data
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive">
                      <HardDrive className="h-4 w-4 mr-2" />
                      {t('clear_all_data_button')}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>{t('clear_all_data_title')}</AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('clear_all_data_desc')}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                      <AlertDialogAction onClick={handleClearAllData}>
                        {t('yes_clear_all')}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-center">{t('username_table_head')}</TableHead>
                  <TableHead className="text-center">{t('role_table_head')}</TableHead>
                  <TableHead className="text-right">{t('actions_table_head')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((u) => (
                  <TableRow key={u.id}>
                    <TableCell className="font-medium text-center">{u.username}</TableCell>
                    <TableCell className="text-center">{roleMap[u.role]}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(u)}> {/* Edit button */}
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            disabled={u.id === user.id} // Can't delete self
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>{t('confirm_delete_title')}</AlertDialogTitle>
                            <AlertDialogDescription>
                              {t('confirm_delete_desc', { username: u.username })}
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(u.id)}>
                              {t('confirm_delete')}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserManagementPage;