import { isValid } from 'date-fns'; // Import isValid for date validation

export function saveToLocalStorage<T>(key: string, data: T): void {
  try {
    // Custom replacer to handle Date objects during serialization
    const replacer = (k: string, value: any) => {
      if (value instanceof Date) {
        return { __type: 'Date', value: value.toISOString() };
      }
      return value;
    };
    localStorage.setItem(key, JSON.stringify(data, replacer));
  } catch (error) {
    console.error(`Error saving to localStorage for key "${key}":`, error);
  }
}

export function loadFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const storedValue = localStorage.getItem(key);
    if (storedValue === null) {
      return defaultValue;
    }
    // Custom reviver to handle Date objects during deserialization
    const reviver = (k: string, value: any) => {
      // Handle custom Date objects saved with __type marker
      if (value && typeof value === 'object' && value.__type === 'Date') {
        const date = new Date(value.value);
        if (isValid(date)) {
          return date;
        }
      }
      // Also try to parse ISO date strings directly if they are not objects
      // This handles cases where dates might have been saved as plain ISO strings
      if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z$/.test(value)) {
        const date = new Date(value);
        if (isValid(date)) {
          return date;
        }
      }
      return value;
    };
    const parsedValue = JSON.parse(storedValue, reviver);
    return parsedValue as T;
  } catch (error) {
    console.error(`Error loading from localStorage for key "${key}":`, error);
    return defaultValue;
  }
}

export function clearLocalStorageItem(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error clearing localStorage for key "${key}":`, error);
  }
}

export function clearAllLocalStorageData(): void {
  try {
    localStorage.clear();
  } catch (error) {
    console.error("Error clearing all localStorage data:", error);
  }
}