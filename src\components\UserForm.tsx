"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth, UserRole, roleMap, User } from '@/context/AuthContext'; // Import User type
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

interface UserFormProps {
  onFinished: () => void;
  userToEdit?: User;
}

export const UserForm = ({ onFinished, userToEdit }: UserFormProps) => {
  const { addUser, updateUser } = useAuth();
  const { t } = useTranslation(); // Use the translation hook

  // Dynamically get roles from roleMap keys for Zod enum
  const roles = Object.keys(roleMap) as [UserRole, ...UserRole[]];

  const formSchema = z.object({
    username: z.string().min(2, { message: t('username_min_length') }),
    password: userToEdit
      ? z.string().optional() // Password is optional for edit
      : z.string().min(6, { message: t('password_min_length') }), // Required for new user
    role: z.enum(roles, { // Use the dynamically generated roles
      errorMap: () => ({ message: t("role_selection_placeholder") }),
    }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: userToEdit ? {
      username: userToEdit.username,
      role: userToEdit.role,
      password: '', // Password field should be empty for editing
    } : {
      username: '',
      password: '',
      role: 'boarding_management', // Default role for new user
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log("Submitting UserForm with values:", values);
    if (userToEdit) {
      const updatedPassword = values.password || userToEdit.password;
      if (updatedPassword === undefined) {
        console.error("Error: Password is undefined during user update.");
        // Potentially show an error toast here
        return;
      }
      updateUser({
        id: userToEdit.id,
        username: values.username,
        password: updatedPassword,
        role: values.role as UserRole,
      });
      showSuccess(t('user_updated_success'));
    } else {
      if (values.password === undefined) {
        console.error("Error: Password is undefined during new user creation.");
        // Potentially show an error toast here
        return;
      }
      addUser({
        username: values.username,
        password: values.password,
        role: values.role as UserRole,
      });
      showSuccess(t('user_added_success'));
    }
    onFinished();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('username_label')}</FormLabel>
              <FormControl>
                <Input placeholder={t('username_label')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('password_label')} {userToEdit && `(${t('optional')})`}</FormLabel>
              <FormControl>
                <Input type="password" placeholder={userToEdit ? t('leave_blank_for_no_change') : t('password_label')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('role_table_head')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('role_selection_placeholder')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(roleMap).map(([key, value]) => (
                    <SelectItem key={key} value={key}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">{userToEdit ? t('update_user_button') : t('add_user_button')}</Button>
      </form>
    </Form>
  );
};