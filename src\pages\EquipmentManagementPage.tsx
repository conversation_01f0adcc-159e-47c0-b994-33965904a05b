"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useEquipment, Equipment, equipmentStatusMap } from '@/context/EquipmentContext';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { EquipmentForm } from '@/components/EquipmentForm';
import { Trash2, Edit, PlusCircle } from 'lucide-react';
import { showSuccess } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation

const EquipmentManagementPage = () => {
  const { user, logout } = useAuth();
  const { equipment, deleteEquipment } = useEquipment();
  const navigate = useNavigate();
  const [isFormOpen, setFormOpen] = useState(false);
  const [equipmentToEdit, setEquipmentToEdit] = useState<Equipment | undefined>(undefined);
  const { t } = useTranslation(); // Use the translation hook

  // Authorization: Only app_admin or equipment_management can access
  if (user?.role !== 'app_admin' && user?.role !== 'equipment_management') {
    navigate('/');
    return null;
  }

  const handleAddNew = () => {
    setEquipmentToEdit(undefined);
    setFormOpen(true);
  };

  const handleEdit = (item: Equipment) => {
    setEquipmentToEdit(item);
    setFormOpen(true);
  };

  const handleDelete = (itemId: number) => {
    deleteEquipment(itemId);
    showSuccess(t('equipment_deleted_success'));
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('equipment_management_title')}</h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
              {t('equipment_management_desc')}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button onClick={() => navigate('/')} variant="outline">
              {t('return_to_dashboard')}
            </Button>
            <Button onClick={logout}>{t('logout_button')}</Button>
          </div>
        </header>

        <Card>
          <CardHeader className="flex flex-row justify-between items-center">
            <CardTitle>{t('equipment_list_title')}</CardTitle>
            <Dialog open={isFormOpen} onOpenChange={setFormOpen}>
              <DialogTrigger asChild>
                <Button onClick={handleAddNew}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  {t('add_new_equipment')}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>{equipmentToEdit ? t('edit_equipment_dialog_title') : t('add_equipment_dialog_title')}</DialogTitle>
                  <DialogDescription>
                    {equipmentToEdit ? t('edit_equipment_dialog_desc') : t('add_equipment_dialog_desc')}
                  </DialogDescription>
                </DialogHeader>
                <EquipmentForm 
                  onFinished={() => setFormOpen(false)} 
                  equipmentToEdit={equipmentToEdit}
                />
              </DialogContent>
            </Dialog>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-center">{t('equipment_name_table_head')}</TableHead>
                  <TableHead className="text-center">{t('equipment_type_table_head')}</TableHead>
                  <TableHead className="text-center">{t('equipment_status_table_head')}</TableHead>
                  <TableHead className="text-center">{t('equipment_usage_count_table_head')}</TableHead>
                  <TableHead className="text-right">{t('actions_table_head')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {equipment.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium text-center">{item.name}</TableCell>
                    <TableCell className="text-center">{item.type}</TableCell>
                    <TableCell className="text-center">{equipmentStatusMap[item.status]}</TableCell>
                    <TableCell className="text-center">{item.usageCount}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(item)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>{t('confirm_delete_title')}</AlertDialogTitle>
                            <AlertDialogDescription>
                              {t('confirm_delete_desc', { username: item.name })}
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(item.id)}>
                              {t('confirm_delete')}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EquipmentManagementPage;