"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { loadFromLocalStorage, saveToLocalStorage, clearLocalStorageItem, clearAllLocalStorageData } from '@/lib/localStorageUtils';

// Define user roles in English for code consistency, map to Arabic for display
export type UserRole = 'app_admin' | 'boarding_management' | 'operations_management' | 'safety_management' | 'equipment_management';

export const roleMap: Record<UserRole, string> = {
  app_admin: 'مسؤول التطبيق', // New role for overall admin
  boarding_management: 'لجنة التراكي', // Renamed
  operations_management: 'إدارة التشغيل',
  safety_management: 'إدارة السلامة',
  equipment_management: 'إدارة المعدات',
};

export interface User { // Exported the User interface
  id: number;
  username: string;
  password: string; // Made password non-optional
  role: UserRole;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  users: User[];
  login: (username: string, password_input: string) => boolean;
  logout: () => void;
  addUser: (user: Omit<User, 'id'>) => void;
  updateUser: (user: User) => void;
  deleteUser: (userId: number) => void;
  clearAllAppData: () => void; // New function to clear all app data
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock initial users (used only if localStorage is empty)
const defaultInitialUsers: User[] = [
  { id: 1, username: 'app_admin', password: 'password', role: 'app_admin' }, // New default admin user
  { id: 2, username: 'boarding_admin', password: 'password', role: 'boarding_management' },
  { id: 3, username: 'ops_manager', password: 'password', role: 'operations_management' },
  { id: 4, username: 'safety_officer', password: 'password', role: 'safety_management' },
];

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [users, setUsers] = useState<User[]>(() => loadFromLocalStorage('app_users', defaultInitialUsers));
  const [user, setUser] = useState<User | null>(() => loadFromLocalStorage('current_user', null));
  const [isAuthenticated, setIsAuthenticated] = useState(!!user);
  const navigate = useNavigate();

  // Save users to localStorage whenever it changes
  useEffect(() => {
    saveToLocalStorage('app_users', users);
  }, [users]);

  // Save current user to localStorage whenever it changes
  useEffect(() => {
    saveToLocalStorage('current_user', user);
    setIsAuthenticated(!!user);
  }, [user]);

  const login = (username: string, password_input: string): boolean => {
    const foundUser = users.find(u => u.username === username && u.password === password_input);
    if (foundUser) {
      setUser(foundUser);
      console.log(`User ${username} logged in with role ${foundUser.role}.`);
      return true;
    }
    console.log('Invalid credentials');
    return false;
  };

  const logout = () => {
    setUser(null);
    console.log('User logged out.');
    navigate('/login');
  };

  const addUser = (newUser: Omit<User, 'id'>) => {
    setUsers(prevUsers => [...prevUsers, { ...newUser, id: Date.now() }]);
  };

  const updateUser = (updatedUser: User) => {
    setUsers(prevUsers => prevUsers.map(u => u.id === updatedUser.id ? updatedUser : u));
    // If the updated user is the currently logged-in user, update the user state
    if (user && user.id === updatedUser.id) {
      setUser(updatedUser);
    }
  };

  const deleteUser = (userId: number) => {
    setUsers(prevUsers => prevUsers.filter(u => u.id !== userId));
    // If the deleted user is the currently logged-in user, log out
    if (user && user.id === userId) {
      logout();
    }
  };

  const clearAllAppData = () => {
    clearAllLocalStorageData();
    // Reset all contexts to their default initial states
    setIsAuthenticated(false);
    setUser(null);
    setUsers(defaultInitialUsers); // Reset to default users
    // Other contexts will also reset as they load from empty localStorage
    navigate('/login'); // Redirect to login after clearing data
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, users, login, logout, addUser, updateUser, deleteUser, clearAllAppData }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};