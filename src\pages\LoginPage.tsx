"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { showSuccess, showError } from '@/utils/toast';
import { useTranslation } from '@/context/TranslationContext'; // Import useTranslation
import { HardDrive } from 'lucide-react'; // Import HardDrive icon
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components

const LoginPage = () => {
  const { login, clearAllAppData } = useAuth(); // Destructure clearAllAppData
  const navigate = useNavigate();
  const { t } = useTranslation(); // Use the translation hook

  const formSchema = z.object({
    username: z.string().min(2, {
      message: t('username_min_length'),
    }),
    password: z.string().min(6, {
      message: t('password_min_length'),
    }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    const isLoggedIn = login(values.username, values.password);
    if (isLoggedIn) {
      showSuccess(t('login_success'));
      navigate('/');
    } else {
      showError(t('login_error'));
    }
  };

  const handleClearAllData = () => {
    clearAllAppData();
    showSuccess(t("clear_all_data_success"));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">{t('login_page_title')}</CardTitle>
          <CardDescription>{t('login_page_desc')}</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('username_label')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('username_label')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('password_label')}</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder={t('password_label')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full">
                {t('login_button')}
              </Button>
            </form>
          </Form>
          <div className="mt-4 text-center">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" className="w-full mt-2">
                  <HardDrive className="h-4 w-4 mr-2" />
                  {t('clear_all_data_button')}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t('clear_all_data_title')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('clear_all_data_desc')}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                  <AlertDialogAction onClick={handleClearAllData}>
                    {t('yes_clear_all')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginPage;