"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { loadFromLocalStorage, saveToLocalStorage } from '@/lib/localStorageUtils';

// Define equipment types and statuses
export type EquipmentStatus = 'new' | 'used' | 'maintained';
export const equipmentStatusMap: Record<EquipmentStatus, string> = {
  new: 'جديدة',
  used: 'مستعملة',
  maintained: 'تمت صيانتها',
};

export interface Equipment {
  id: number;
  name: string;
  type: string; // e.g., 'Legs', 'Lock', 'Hook'
  status: EquipmentStatus;
  usageCount: number;
  purchasePrice: number;
  lifespanEstimate: string; // e.g., '5 years'
}

interface EquipmentContextType {
  equipment: Equipment[];
  addEquipment: (item: Omit<Equipment, 'id' | 'usageCount'>) => void;
  updateEquipment: (item: Equipment) => void;
  deleteEquipment: (itemId: number) => void;
}

const EquipmentContext = createContext<EquipmentContextType | undefined>(undefined);

// Mock initial equipment data (used only if localStorage is empty)
const defaultInitialEquipment: Equipment[] = [
  { id: 1, name: 'Hook-A1', type: 'هوك', status: 'new', usageCount: 0, purchasePrice: 500, lifespanEstimate: '5 years' },
  { id: 2, name: 'Leg-B2', type: 'ليجز', status: 'used', usageCount: 15, purchasePrice: 1200, lifespanEstimate: '3 years' },
  { id: 3, name: 'Lock-C3', type: 'قفل', status: 'maintained', usageCount: 45, purchasePrice: 300, lifespanEstimate: '2 years' },
];

export const EquipmentProvider = ({ children }: { children: ReactNode }) => {
  const [equipment, setEquipment] = useState<Equipment[]>(() => loadFromLocalStorage('app_equipment', defaultInitialEquipment));

  // Save equipment to localStorage whenever it changes
  useEffect(() => {
    saveToLocalStorage('app_equipment', equipment);
  }, [equipment]);

  const addEquipment = (item: Omit<Equipment, 'id' | 'usageCount'>) => {
    setEquipment(prev => [...prev, { ...item, id: Date.now(), usageCount: 0 }]);
  };

  const updateEquipment = (updatedItem: Equipment) => {
    setEquipment(prev => prev.map(item => item.id === updatedItem.id ? updatedItem : item));
  };

  const deleteEquipment = (itemId: number) => {
    setEquipment(prev => prev.filter(item => item.id !== itemId));
  };

  return (
    <EquipmentContext.Provider value={{ equipment, addEquipment, updateEquipment, deleteEquipment }}>
      {children}
    </EquipmentContext.Provider>
  );
};

export const useEquipment = () => {
  const context = useContext(EquipmentContext);
  if (context === undefined) {
    throw new Error('useEquipment must be used within an EquipmentProvider');
  }
  return context;
};