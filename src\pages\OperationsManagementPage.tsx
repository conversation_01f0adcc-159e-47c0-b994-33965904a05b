"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useOperations, Operation } from '@/context/OperationsContext';
import { useBoarding } from '@/context/BoardingContext';
import { useEquipment } from '@/context/EquipmentContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useNavigate } from 'react-router-dom';
import { Activity, PlusCircle, Edit, Trash2 } from 'lucide-react';
import { OperationForm } from '@/components/OperationForm';
import { showSuccess } from '@/utils/toast';
import { format, isValid } from 'date-fns'; // Import isValid
import { useTranslation } from '@/context/TranslationContext';

const OperationsManagementPage = () => {
  const { user, logout } = useAuth();
  const { operations, deleteOperation } = useOperations();
  const { voyages } = useBoarding();
  const { equipment, updateEquipment } = useEquipment();
  const navigate = useNavigate();
  const [isFormOpen, setFormOpen] = useState(false);
  const [operationToEdit, setOperationToEdit] = useState<Operation | undefined>(undefined);
  const { t } = useTranslation();

  // Authorization: Only app_admin, operations_management or boarding_management can access
  if (user?.role !== 'app_admin' && user?.role !== 'operations_management' && user?.role !== 'boarding_management') {
    navigate('/');
    return null;
  }

  const getVoyageName = (voyageId: number) => voyages.find(v => v.id === voyageId)?.shipName || t('unknown_voyage');
  const getEquipmentDetails = (equipmentIds: number[]) => 
    equipment
      .filter(e => equipmentIds.includes(e.id))
      .map(e => ({ name: e.name, usageCount: e.usageCount }));

  const handleAddNew = () => {
    setOperationToEdit(undefined);
    setFormOpen(true);
  };

  const handleEdit = (op: Operation) => {
    setOperationToEdit(op);
    setFormOpen(true);
  };

  const handleDelete = (opId: number) => {
    const opToDelete = operations.find(op => op.id === opId);
    if (opToDelete) {
      // Decrement usage count for associated equipment
      opToDelete.equipmentIds.forEach(eqId => {
        const eq = equipment.find(e => e.id === eqId);
        if (eq) updateEquipment({ ...eq, usageCount: Math.max(0, eq.usageCount - 1) });
      });
      deleteOperation(opId);
      showSuccess(t('operation_deleted_success'));
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('operations_management_title')}</h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
              {t('operations_management_desc')}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button onClick={() => navigate('/')} variant="outline">
              {t('return_to_dashboard')}
            </Button>
            <Button onClick={logout}>{t('logout_button')}</Button>
          </div>
        </header>

        <Card>
          <CardHeader className="flex flex-row justify-between items-center">
            <CardTitle>{t('current_operations_title')}</CardTitle>
            <Dialog open={isFormOpen} onOpenChange={setFormOpen}>
              <DialogTrigger asChild>
                <Button onClick={handleAddNew}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  {t('add_new_operation')}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>{operationToEdit ? t('edit_operation_dialog_title') : t('add_operation_dialog_title')}</DialogTitle>
                  <DialogDescription>
                    {operationToEdit ? t('edit_operation_dialog_desc') : t('add_operation_dialog_desc')}
                  </DialogDescription>
                </DialogHeader>
                <OperationForm
                  onFinished={() => setFormOpen(false)}
                  operationToEdit={operationToEdit}
                />
              </DialogContent>
            </Dialog>
          </CardHeader>
          <CardContent>
            {operations.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-center">{t('operation_table_head')}</TableHead>
                    <TableHead className="text-center">{t('ship_table_head')}</TableHead>
                    <TableHead className="text-center">{t('used_equipment_table_head')}</TableHead>
                    <TableHead className="text-center">{t('notes_table_head')}</TableHead>
                    <TableHead className="text-center">{t('created_at_table_head')}</TableHead>
                    <TableHead className="text-center">{t('updated_at_table_head')}</TableHead>
                    <TableHead className="text-right">{t('actions_table_head')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {operations.map((op) => {
                    const usedEquipment = getEquipmentDetails(op.equipmentIds);
                    return (
                      <TableRow key={op.id}>
                        <TableCell className="font-medium text-center">{t('operation_table_head')} #{op.id}</TableCell>
                        <TableCell className="text-center">{getVoyageName(op.voyageId)}</TableCell>
                        <TableCell className="text-center">
                          {usedEquipment.length > 0 ? (
                            usedEquipment.map((eq, index) => (
                              <div key={index}>
                                {eq.name} ({eq.usageCount})
                              </div>
                            ))
                          ) : (
                            t('no_equipment')
                          )}
                        </TableCell>
                        <TableCell className="text-center">{op.notes || t('no_notes')}</TableCell>
                        <TableCell className="text-center">
                          {op.createdAt && isValid(op.createdAt) ? format(op.createdAt, 'PPP p') : 'N/A'}
                        </TableCell>
                        <TableCell className="text-center">
                          {op.updatedAt && isValid(op.updatedAt) ? format(op.updatedAt, 'PPP p') : 'N/A'}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="icon" onClick={() => handleEdit(op)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>{t('confirm_delete_title')}</AlertDialogTitle>
                                <AlertDialogDescription>
                                  {t('confirm_delete_desc', { username: `${t('operation_table_head')} #${op.id}` })}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDelete(op.id)}>
                                  {t('confirm_delete')}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <p className="text-center text-gray-500 dark:text-gray-400 py-8">
                {t('no_operations_currently')}
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OperationsManagementPage;